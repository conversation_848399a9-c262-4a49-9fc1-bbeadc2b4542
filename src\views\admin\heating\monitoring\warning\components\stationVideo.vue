<template>
  <div class="gas-leak-monitor-video" v-if="!showMapMode">
    <!-- 顶部统计信息 -->
    <div class="status-container">
      <div class="status-cards">
        <div class="status-card bg-blue-light">
          <div class="icon-box">
            <span class="text-blue-500 text-xl">
              <i class="el-icon-video-camera"></i>
            </span>
          </div>
          <div class="content">
            <div class="title">全部设备</div>
            <div class="number">{{ videoChannelCount.total }}</div>
          </div>
        </div>

        <div class="status-card bg-green-light">
          <div class="icon-box">
            <span class="text-green-500 text-xl">
              <i class="el-icon-check"></i>
            </span>
          </div>
          <div class="content">
            <div class="title">正常</div>
            <div class="number">{{ videoChannelCount.online }}</div>
          </div>
        </div>

        <div class="status-card bg-yellow-light">
          <div class="icon-box">
            <span class="text-yellow-500 text-xl">
              <i class="el-icon-warning"></i>
            </span>
          </div>
          <div class="content">
            <div class="title">离线</div>
            <div class="number">{{ videoChannelCount.offline }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索栏和地图模式按钮 -->
    <div class="flex justify-between mb-4">
      <div class="search-container">
        <div class="search-form">
          <div class="form-item">
            <span class="label">设备状态:</span>
            <el-select v-model="searchForm.online" class="form-select" placeholder="请选择" clearable>
              <el-option label="全部" value="" />
              <el-option label="在线" :value="true" />
              <el-option label="离线" :value="false" />
            </el-select>
          </div>
          <div class="form-item">
            <el-input v-model="searchForm.name" placeholder="输入监控设备名称" prefix-icon="el-icon-search" class="search-input"
               clearable />
          </div>
          <div class="form-item">
            <el-button type="primary" class="search-btn" @click="handleSearchClick">查询</el-button>
            <el-button class="reset-btn" @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content-area">
      <!-- 左侧站点列表 -->
      <div class="left-sidebar">
        <!-- 视频通道树 -->
        <div class="video-tree">
          <el-tree :data="videoTreeData" :props="treeProps" node-key="id" :show-checkbox="false"
            :check-on-click-node="false" v-loading="treeLoading" ref="treeRef">
            <template #default="{ node, data }">
              <div class="tree-node" :class="{ 'parent-node': data.data?.parental === 1 }">
                <!-- 只有非父组件（parental !== 1）的节点显示勾选框 -->
                <el-checkbox v-if="data.data?.parental !== 1"
                  :model-value="playingVideos.some(v => v.id === data.id)" :disabled="!data.data?.online"
                  @change="(checked) => handleNodeCheckChange(data, checked)" class="node-checkbox" />
                <span class="node-label" :class="{ 'parent-label': data.data?.parental === 1 }">{{ node.label }}</span>
                <!-- 只有非父组件（parental !== 1）的节点显示状态 -->
                <span v-if="data.data?.parental !== 1"
                  :class="['node-status', data.data?.online ? 'status-online' : 'status-offline']">
                  {{ data.data?.online ? '在线' : '离线' }}
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧视频展示区域 -->
      <div class="right-content">
        <!-- 视频标签和地图模式按钮 -->
        <div class="video-header">
          <div class="video-tags">
            <el-tag v-for="(video, index) in playingVideos" :key="video.id" closable @close="closeVideo(index)"
              class="video-tag">
              {{ video.name }}
            </el-tag>
          </div>
          <el-tooltip effect="dark" content="地图模式" placement="top">
            <img class="map-icon" src="@/assets/images/mis/show_map.png" @click="hangleMapMode(true)" alt="" />
          </el-tooltip>
        </div>

        <!-- 视频监控区域 -->
        <div class="video-content">
          <!-- 视频监控网格 -->
          <div class="video-grid">
            <div v-for="(video, index) in videoSlots" :key="index" class="video-item">
              <!-- 有视频时显示播放器 -->
              <div v-if="video" class="video-container">
                <VideoPlayer :src="video.hlsUrl" :autoplay="true" :muted="true" :loop="true" :showControls="true"
                  :showStatus="true" :isOnline="video.online" class="video-player" />
                <!-- <div class="video-info">
                  <span class="video-title">{{ video.name }}</span>
                  <span class="video-status" :class="video.online ? 'status-online' : 'status-offline'">
                    {{ video.online ? '在线' : '离线' }}
                  </span>
                </div> -->
              </div>

              <!-- 空白插槽 -->
              <div v-else class="video-placeholder">
                <div class="placeholder-content">
                  <i class="el-icon-video-camera placeholder-icon"></i>
                  <div class="placeholder-text">暂无预览信息</div>
                </div>
                <div class="video-info">
                  <span class="video-title">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <el-tooltip v-if="showMapMode" effect="dark" content="列表模式" placement="top">
    <img class="map-icon top-icon" src="@/assets/images/mis/show_list.png" @click="hangleMapMode(false)" alt="" />
  </el-tooltip>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick, h, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import VideoPlayer from '@/components/screen/common/VideoPlayer.vue';
import emitter from "@/utils/mitt.js";
import { getVideoChannelTreeList, getVideoStreamHls, getVideoChannelCount } from '@/api/base.js';


const router = useRouter();
const showMapMode = ref(false);
const treeLoading = ref(false);
const videoTreeData = ref([]);
const playingVideos = ref([]);
const treeRef = ref(null);
const videoChannelCount = ref({});

// 搜索表单数据
const searchForm = reactive({
  name: '',
  online: ''
});
// 树组件配置
const treeProps = {
  children: 'children',
  label: 'name'
};

// 固定6个视频插槽
const videoSlots = computed(() => {
  const slots = new Array(6).fill(null);
  playingVideos.value.forEach((video, index) => {
    if (index < 6) {
      slots[index] = video;
    }
  });
  return slots;
});
const getVideoChannelCounts = async () => {
  const response = await getVideoChannelCount();
  if (response.code === 200) {
    videoChannelCount.value = response.data;
  } else {
    ElMessage.error(response.message || '获取视频通道统计信息失败');
  }
};
// 获取视频通道树列表
const getVideoChannelTree = async (params = {}) => {
  try {
    treeLoading.value = true;
    const response = await getVideoChannelTreeList(params);
    if (response.code === 200) {
      videoTreeData.value = formatTreeData(response.data);
    } else {
      ElMessage.error(response.message || '获取视频通道树失败');
    }
  } catch (error) {
    console.error('获取视频通道树失败:', error);
    ElMessage.error('获取视频通道树失败');
  } finally {
    treeLoading.value = false;
  }
};

// 格式化树数据，为每个节点添加唯一ID
const formatTreeData = (data) => {
  return data.map(item => {
    const node = {
      ...item,
      id: item.data?.id || `${item.name}_${Date.now()}_${Math.random()}`,
      children: item.children ? formatTreeData(item.children) : []
    };
    return node;
  });
};

// 搜索处理 - 实时搜索（输入框变化时）
let searchTimer = null;
const handleSearch = () => {
  clearTimeout(searchTimer);
  searchTimer = setTimeout(() => {
    const params = {
      name: searchForm.name || '',
      online: searchForm.online !== '' ? searchForm.online : undefined
    };
    getVideoChannelTree(params);
  }, 500);
};

// 查询按钮点击处理
const handleSearchClick = () => {
  const params = {
    name: searchForm.name || '',
    online: searchForm.online !== '' ? searchForm.online : undefined
  };
  getVideoChannelTree(params);
};

// 重置按钮处理
const handleReset = () => {
  searchForm.name = '';
  searchForm.online = '';
  getVideoChannelTree();
};

// 节点勾选变化处理
const handleNodeCheckChange = async (data, checked) => {
  // 只处理非父组件的节点
  if (data.data?.parental === 1) {
    return;
  }

  if (checked) {
    // 选中节点，尝试播放视频
    await playVideo(data);
  } else {
    // 取消选中，关闭视频
    closeVideoByData(data);
  }
};

// 播放视频
const playVideo = async (nodeData) => {
  // 父组件不能播放视频
  if (nodeData.data?.parental === 1) {
    return;
  }

  if (playingVideos.value.length >= 6) {
    ElMessage.warning('最多只能同时播放6个视频，请先关闭一个视频');
    return;
  }

  if (playingVideos.value.find(v => v.id === nodeData.id)) {
    return; // 已经在播放
  }

  try {
    const response = await getVideoStreamHls({
      deviceId: nodeData.data.deviceId,
      channelId: nodeData.data.channelId
    });

    if (response.code === 200 && response.data) {
      const videoInfo = {
        id: nodeData.id,
        name: nodeData.name,
        deviceId: nodeData.data.deviceId,
        channelId: nodeData.data.channelId,
        hlsUrl: response.data.hlsUrl,
        online: response.data.online !== false ? (nodeData.data.online || true) : false
      };

      playingVideos.value.push(videoInfo);
    } else {
      ElMessage.error('获取视频流失败');
    }
  } catch (error) {
    console.error('获取视频流失败:', error);
    ElMessage.error('获取视频流失败');
  }
};

// 关闭视频（通过索引）
const closeVideo = (index) => {
  if (index >= 0 && index < playingVideos.value.length) {
    const video = playingVideos.value[index];
    playingVideos.value.splice(index, 1);
  }
};

// 关闭视频（通过数据）
const closeVideoByData = (nodeData) => {
  const index = playingVideos.value.findIndex(v => v.id === nodeData.id);
  if (index !== -1) {
    playingVideos.value.splice(index, 1);
  }
};

// 切换地图模式
const hangleMapMode = (show) => {
  showMapMode.value = show;
  emitter.emit("handleMisMapShow", show);
};

onMounted(() => {
  getVideoChannelTree();
  getVideoChannelCounts();
});

onUnmounted(() => {
  // 确保所有视频资源都被释放
  playingVideos.value = [];
});
</script>

<style scoped>
.gas-leak-monitor-video {
  padding: 16px;
}

.status-container {
  width: 100%;
  height: 132px;
  background: #FFFFFF;
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.status-cards {
  display: flex;
  gap: 16px;
  width: 100%;
}

.status-card {
  width: 380px;
  height: 100px;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  align-items: center;
}

.bg-blue-light {
  background: #F1F8FF;
}

.bg-green-light {
  background: #E7FFFA;
}

.bg-yellow-light {
  background: #FFF7E7;
}

.icon-box {
  margin-right: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.content {
  display: flex;
  flex-direction: column;
}

.title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #000000;
  margin-bottom: 8px;
}

.number {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 30px;
  color: #000000;
}

/* 主内容区域样式 */
.main-content-area {
  display: flex;
  gap: 16px;
  width: 100%;
  height: calc(100vh - 240px);
  min-height: 600px;
}

/* 左侧侧边栏样式 */
.left-sidebar {
  width: 350px;
  background: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
  flex-shrink: 0;
  height: 100%;
  overflow: hidden;
}

.video-tree {
  height: 80%;
  overflow-x: auto;
  overflow-y: auto;
  /* padding-right: 4px; */
}

/* 树节点样式 */
.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  padding-right: 20px;
  min-width: 300px;
}

.node-checkbox {
  margin-right: 8px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 10px;
  min-width: 0;
}

.node-status {
  font-size: 12px;
  flex-shrink: 0;
  white-space: nowrap;
  font-weight: 500;
}

.status-online {
  color: #52c41a !important;
}

.status-offline {
  color: #999999 !important;
}

/* 右侧内容区域样式 */
.right-content {
  flex: 1;
  min-width: 0;
  background: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  flex-shrink: 0;
}

.video-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  flex: 1;
}

.video-tag {
  background: #F1F8FF !important;
  color: #1890ff !important;
  border-color: #d9e9ff !important;
}

.video-content {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  height: auto;
}

.video-item {
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 240px;
}

.video-container,
.video-placeholder {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.video-player {
  width: 100%;
  height: 200px;
  border-radius: 4px 4px 0 0;
  overflow: hidden;
  flex-shrink: 0;
  display: block;
}

.video-placeholder {
  height: 200px;
  background: #f7f7f7;
  display: flex;
  flex-direction: column;
  border-radius: 4px 4px 0 0;
}

.placeholder-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  font-size: 32px;
  color: #ccc;
  margin-bottom: 8px;
}

.placeholder-text {
  color: #999;
  font-size: 12px;
}

.video-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f7f7f7;
  border-radius: 0 0 4px 4px;
  min-height: 40px;
  flex-shrink: 0;
}

.video-title {
  font-size: 14px;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-status {
  font-size: 12px;
  margin-left: 8px;
}

.map-icon {
  width: 34px;
  height: 34px;
  cursor: pointer;
  pointer-events: all;
}

.top-icon {
  position: fixed;
  top: 265px;
  width: 38px;
  height: 38px;
  right: 28px;
  z-index: 999;
}

/* 响应式布局 */
@media (max-height: 1080px) {
  .main-content-area {
    height: calc(100vh - 220px);
    min-height: 500px;
  }

  .video-item {
    height: 220px;
  }

  .video-player {
    width: 100%;
    height: 180px;
  }

  .video-placeholder {
    height: 180px;
  }

  .video-grid {
    gap: 10px;
  }
}

@media (max-height: 900px) {
  .main-content-area {
    height: calc(100vh - 200px);
    min-height: 450px;
  }

  .video-item {
    height: 200px;
  }

  .video-player {
    width: 100%;
    height: 160px;
  }

  .video-placeholder {
    height: 160px;
  }

  .video-grid {
    gap: 8px;
  }
}

@media (max-height: 800px) {
  .main-content-area {
    height: calc(100vh - 180px);
    min-height: 400px;
  }

  .video-item {
    height: 180px;
  }

  .video-player {
    width: 100%;
    height: 140px;
  }

  .video-placeholder {
    height: 140px;
  }

  .video-grid {
    gap: 6px;
  }
}

/* Element UI 样式覆盖 */
:deep(.el-tree-node__content) {
  height: auto !important;
  padding: 8px 0 !important;
  min-width: 300px !important;
  overflow: visible !important;
}

:deep(.el-tree-node__expand-icon) {
  padding: 6px;
  flex-shrink: 0;
}

:deep(.el-tree-node__label) {
  flex: 1;
  min-width: 0;
  width: 100%;
}

:deep(.el-tree) {
  overflow-x: auto !important;
  width: 100%;
}

:deep(.el-tree-node) {
  white-space: nowrap !important;
  min-width: 300px !important;
}

:deep(.el-tree-node > .el-tree-node__content) {
  overflow: visible !important;
}

/* 确保勾选框样式正确 */
:deep(.el-checkbox) {
  margin-right: 8px;
  flex-shrink: 0;
}

:deep(.el-checkbox__input.is-disabled .el-checkbox__inner) {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  cursor: not-allowed;
}

:deep(.el-checkbox__input.is-disabled + span.el-checkbox__label) {
  color: #c0c4cc !important;
  cursor: not-allowed;
}

/* 搜索框样式 */
.search-container {
  width: 100%;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: nowrap;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  white-space: nowrap;
  margin: 0;
}

.search-input {
  width: 300px;
}

.form-select {
  width: 150px;
}

.search-btn {
  background: #1890FF;
  border-color: #1890FF;
  color: #FFFFFF;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  height: 32px;
  padding: 0 16px;
  border-radius: 4px;
  margin-right: 8px;
}

.search-btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.reset-btn {
  background: #FFFFFF;
  border-color: #CED3DA;
  color: #282828;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  height: 32px;
  padding: 0 16px;
  border-radius: 4px;
}

.reset-btn:hover {
  border-color: #1890FF;
  color: #1890FF;
}

/* 统一输入框和下拉框的高度和样式 */
:deep(.el-input) {
  height: 32px;
}

:deep(.el-input__wrapper) {
  height: 32px;
  box-shadow: 0 0 0 1px #CED3DA inset;
  border-radius: 4px;
}

:deep(.el-select) {
  height: 32px;
}

:deep(.el-select .el-input) {
  height: 32px;
}

:deep(.el-select .el-input__wrapper) {
  height: 32px;
  box-shadow: 0 0 0 1px #CED3DA inset;
  border-radius: 4px;
}

:deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
}

/* 确保按钮和输入框对齐 */
:deep(.el-button) {
  height: 32px;
  line-height: 30px;
}
</style>
